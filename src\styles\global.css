/* 响应式全局样式 - 基于390px设计稿适配 */

/* 字体设置 */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');

/* 响应式全局变量 - 继承uni.scss的适配系统 */
:root {
  /* 颜色系统 - 保持与uni.scss一致 */
  --primary-color: #00b4d8;
  --secondary-color: #0096c7;
  --accent-color: #f9ad3d;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --info-color: #2196f3;
  
  /* 背景色 */
  --bg-primary: linear-gradient(120deg, #e3f0ff 0%, #f7f7f7 100%);
  --bg-secondary: #f0f0f0;
  --bg-card: #ffffff;
  --bg-overlay: rgba(255, 255, 255, 0.95);
  
  /* 文字颜色 */
  --text-primary: #222;
  --text-secondary: #666;
  --text-muted: #999;
  --text-light: #aaa;
  
  /* 阴影 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.08);
  --shadow-xl: 0 20px 60px rgba(0, 0, 0, 0.12);
  
  /* 圆角 - 使用uni.scss的响应式变量 */
  --radius-sm: var(--radius-sm);
  --radius-md: var(--radius-lg);
  --radius-lg: var(--radius-xl);
  --radius-xl: var(--radius-2xl);
  --radius-full: var(--radius-full);

  /* 间距 - 使用uni.scss的响应式变量 */
  --space-xs: var(--space-xs);
  --space-sm: var(--space-sm);
  --space-md: var(--space-base);
  --space-lg: var(--space-lg);
  --space-xl: var(--space-xl);
  --space-2xl: var(--space-2xl);
  
  /* 字体大小 - 使用uni.scss的响应式变量 */
  --text-xs: var(--font-xs);
  --text-sm: var(--font-sm);
  --text-base: var(--font-base);
  --text-lg: var(--font-lg);
  --text-xl: var(--font-xl);
  --text-2xl: var(--font-2xl);
  --text-3xl: var(--font-3xl);
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-base: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* 全局重置 */
* {
  box-sizing: border-box;
}

page {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 通用动画类 */
.animate-fadeIn {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-slideUp {
  animation: slideUp 0.6s ease-out forwards;
}

.animate-slideDown {
  animation: slideDown 0.6s ease-out forwards;
}

.animate-slideLeft {
  animation: slideLeft 0.6s ease-out forwards;
}

.animate-slideRight {
  animation: slideRight 0.6s ease-out forwards;
}

.animate-bounce {
  animation: bounce 1s ease-in-out infinite;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideLeft {
  from {
    opacity: 0;
    transform: translateX(30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideRight {
  from {
    opacity: 0;
    transform: translateX(-30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30rpx, 0);
  }
  70% {
    transform: translate3d(0, -15rpx, 0);
  }
  90% {
    transform: translate3d(0, -4rpx, 0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

/* 通用工具类 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

.rounded {
  border-radius: var(--radius-md);
}

.rounded-lg {
  border-radius: var(--radius-lg);
}

.rounded-full {
  border-radius: var(--radius-full);
}

.shadow {
  box-shadow: var(--shadow-md);
}

.shadow-lg {
  box-shadow: var(--shadow-lg);
}

.transition {
  transition: all var(--transition-base);
}

.transition-fast {
  transition: all var(--transition-fast);
}

.transition-slow {
  transition: all var(--transition-slow);
}

/* 响应式设计 - 使用uni.scss的断点系统 */
/* 媒体查询已在uni.scss中统一管理，这里只做补充 */

/* 组件级响应式调整 - 使用比例缩放保持布局一致性 */
/* 基于iPhone 14 Pro Max (430px) 为标准的比例缩放系统 */

/* 极小屏幕 (< 280px) - 75%缩放 */
@media (max-width: 279px) {
  .responsive-container {
    transform: scale(0.75);
    transform-origin: top left;
    width: 133.33%; /* 1/0.75 = 1.333 */
  }
}

/* 小屏幕 (280px - 319px) - 85%缩放 */
@media (min-width: 280px) and (max-width: 319px) {
  .responsive-container {
    transform: scale(0.85);
    transform-origin: top left;
    width: 117.65%; /* 1/0.85 = 1.176 */
  }
}

/* 中小屏幕 (320px - 374px) - 92%缩放 */
@media (min-width: 320px) and (max-width: 374px) {
  .responsive-container {
    transform: scale(0.92);
    transform-origin: top left;
    width: 108.7%; /* 1/0.92 = 1.087 */
  }
}

/* 中等屏幕 (375px - 429px) - 渐进缩放到100% */
@media (min-width: 375px) and (max-width: 429px) {
  .responsive-container {
    transform: scale(calc(0.92 + (100vw - 375px) * 0.08 / 54));
    transform-origin: top left;
    width: calc(108.7% - (100vw - 375px) * 8.7 / 54);
  }
}

/* 标准屏幕 (430px) - 100%基准，无需缩放 */
/* iPhone 14 Pro Max 及类似尺寸设备 */

/* 中大屏幕 (430px - 479px) - 105%缩放 */
@media (min-width: 430px) and (max-width: 479px) {
  .responsive-container {
    transform: scale(1.05);
    transform-origin: top left;
    width: 95.24%; /* 1/1.05 = 0.952 */
  }
}

/* 大屏幕 (480px - 767px) - 110%缩放 */
@media (min-width: 480px) and (max-width: 767px) {
  .responsive-container {
    transform: scale(1.1);
    transform-origin: top left;
    width: 90.91%; /* 1/1.1 = 0.909 */
  }
}

/* 超大屏幕 (768px+) - 115%缩放 */
@media (min-width: 768px) {
  .responsive-container {
    transform: scale(1.15);
    transform-origin: top left;
    width: 86.96%; /* 1/1.15 = 0.870 */
    max-width: 800px; /* 限制最大宽度 */
    margin: 0 auto; /* 居中显示 */
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: linear-gradient(120deg, #1a1a2e 0%, #16213e 100%);
    --bg-secondary: #2a2a3e;
    --bg-card: #3a3a4e;
    --bg-overlay: rgba(58, 58, 78, 0.95);
    
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #999999;
    --text-light: #666666;
    
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.4);
    --shadow-xl: 0 20px 60px rgba(0, 0, 0, 0.5);
  }
}
