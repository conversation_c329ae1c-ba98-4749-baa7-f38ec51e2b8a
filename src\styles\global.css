/* 响应式全局样式 - 基于390px设计稿适配 */

/* 字体设置 */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');

/* 响应式全局变量 - 继承uni.scss的适配系统 */
:root {
  /* 颜色系统 - 保持与uni.scss一致 */
  --primary-color: #00b4d8;
  --secondary-color: #0096c7;
  --accent-color: #f9ad3d;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --info-color: #2196f3;
  
  /* 背景色 */
  --bg-primary: linear-gradient(120deg, #e3f0ff 0%, #f7f7f7 100%);
  --bg-secondary: #f0f0f0;
  --bg-card: #ffffff;
  --bg-overlay: rgba(255, 255, 255, 0.95);
  
  /* 文字颜色 */
  --text-primary: #222;
  --text-secondary: #666;
  --text-muted: #999;
  --text-light: #aaa;
  
  /* 阴影 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.08);
  --shadow-xl: 0 20px 60px rgba(0, 0, 0, 0.12);
  
  /* 圆角 - 使用uni.scss的响应式变量 */
  --radius-sm: var(--radius-sm);
  --radius-md: var(--radius-lg);
  --radius-lg: var(--radius-xl);
  --radius-xl: var(--radius-2xl);
  --radius-full: var(--radius-full);

  /* 间距 - 使用uni.scss的响应式变量 */
  --space-xs: var(--space-xs);
  --space-sm: var(--space-sm);
  --space-md: var(--space-base);
  --space-lg: var(--space-lg);
  --space-xl: var(--space-xl);
  --space-2xl: var(--space-2xl);
  
  /* 字体大小 - 使用uni.scss的响应式变量 */
  --text-xs: var(--font-xs);
  --text-sm: var(--font-sm);
  --text-base: var(--font-base);
  --text-lg: var(--font-lg);
  --text-xl: var(--font-xl);
  --text-2xl: var(--font-2xl);
  --text-3xl: var(--font-3xl);
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-base: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* 全局重置 */
* {
  box-sizing: border-box;
}

page {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 通用动画类 */
.animate-fadeIn {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-slideUp {
  animation: slideUp 0.6s ease-out forwards;
}

.animate-slideDown {
  animation: slideDown 0.6s ease-out forwards;
}

.animate-slideLeft {
  animation: slideLeft 0.6s ease-out forwards;
}

.animate-slideRight {
  animation: slideRight 0.6s ease-out forwards;
}

.animate-bounce {
  animation: bounce 1s ease-in-out infinite;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideLeft {
  from {
    opacity: 0;
    transform: translateX(30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideRight {
  from {
    opacity: 0;
    transform: translateX(-30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30rpx, 0);
  }
  70% {
    transform: translate3d(0, -15rpx, 0);
  }
  90% {
    transform: translate3d(0, -4rpx, 0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

/* 通用工具类 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

.rounded {
  border-radius: var(--radius-md);
}

.rounded-lg {
  border-radius: var(--radius-lg);
}

.rounded-full {
  border-radius: var(--radius-full);
}

.shadow {
  box-shadow: var(--shadow-md);
}

.shadow-lg {
  box-shadow: var(--shadow-lg);
}

.transition {
  transition: all var(--transition-base);
}

.transition-fast {
  transition: all var(--transition-fast);
}

.transition-slow {
  transition: all var(--transition-slow);
}

/* 响应式设计 - 使用uni.scss的断点系统 */
/* 媒体查询已在uni.scss中统一管理，这里只做补充 */

/* 通用响应式系统 - 基于iPhone 14 Pro Max (430px) 为标准 */
/* 使用连续缩放，适配所有屏幕宽度，保持布局结构不变 */

:root {
  /* 基于视口宽度的动态缩放因子 */
  /* 公式: scale = clamp(0.7, 100vw / 430px, 1.3) */
  --viewport-scale: clamp(0.7, calc(100vw / 430px), 1.3);
  --viewport-scale-inverse: calc(1 / var(--viewport-scale));

  /* 动态字体大小 - 基于视口缩放 */
  --font-xs-fluid: calc(24rpx * var(--viewport-scale));
  --font-sm-fluid: calc(28rpx * var(--viewport-scale));
  --font-base-fluid: calc(32rpx * var(--viewport-scale));
  --font-lg-fluid: calc(36rpx * var(--viewport-scale));
  --font-xl-fluid: calc(40rpx * var(--viewport-scale));
  --font-2xl-fluid: calc(48rpx * var(--viewport-scale));
  --font-3xl-fluid: calc(56rpx * var(--viewport-scale));

  /* 动态间距 - 基于视口缩放 */
  --space-xs-fluid: calc(8rpx * var(--viewport-scale));
  --space-sm-fluid: calc(12rpx * var(--viewport-scale));
  --space-base-fluid: calc(16rpx * var(--viewport-scale));
  --space-lg-fluid: calc(24rpx * var(--viewport-scale));
  --space-xl-fluid: calc(32rpx * var(--viewport-scale));
  --space-2xl-fluid: calc(48rpx * var(--viewport-scale));
  --space-3xl-fluid: calc(64rpx * var(--viewport-scale));

  /* 动态圆角 */
  --radius-xs-fluid: calc(4rpx * var(--viewport-scale));
  --radius-sm-fluid: calc(8rpx * var(--viewport-scale));
  --radius-base-fluid: calc(12rpx * var(--viewport-scale));
  --radius-lg-fluid: calc(16rpx * var(--viewport-scale));
  --radius-xl-fluid: calc(24rpx * var(--viewport-scale));
}

/* 通用网格布局保持规则 - 适用于所有屏幕尺寸 */
.responsive-grid,
.info-cards-section,
.stats-cards,
.time-weather-section,
.data-grid,
.quick-overview,
.secondary-data-grid,
.bottom-data-grid,
.info-grid-redesigned {
  /* 强制保持原始网格结构 */
  grid-template-columns: repeat(2, 1fr) !important;
  gap: var(--space-base-fluid) !important;
}

/* 通用容器样式 - 使用流体缩放 */
.responsive-container {
  width: 100%;
  padding: var(--space-base-fluid);
  margin: 0 auto;
  /* 在大屏幕上限制最大宽度 */
  max-width: calc(800px * var(--viewport-scale));
}

/* 通用文字大小应用 */
.text-xs { font-size: var(--font-xs-fluid) !important; }
.text-sm { font-size: var(--font-sm-fluid) !important; }
.text-base { font-size: var(--font-base-fluid) !important; }
.text-lg { font-size: var(--font-lg-fluid) !important; }
.text-xl { font-size: var(--font-xl-fluid) !important; }
.text-2xl { font-size: var(--font-2xl-fluid) !important; }
.text-3xl { font-size: var(--font-3xl-fluid) !important; }

/* 通用间距应用 */
.p-xs { padding: var(--space-xs-fluid) !important; }
.p-sm { padding: var(--space-sm-fluid) !important; }
.p-base { padding: var(--space-base-fluid) !important; }
.p-lg { padding: var(--space-lg-fluid) !important; }
.p-xl { padding: var(--space-xl-fluid) !important; }

.m-xs { margin: var(--space-xs-fluid) !important; }
.m-sm { margin: var(--space-sm-fluid) !important; }
.m-base { margin: var(--space-base-fluid) !important; }
.m-lg { margin: var(--space-lg-fluid) !important; }
.m-xl { margin: var(--space-xl-fluid) !important; }

/* 通用圆角应用 */
.rounded-xs { border-radius: var(--radius-xs-fluid) !important; }
.rounded-sm { border-radius: var(--radius-sm-fluid) !important; }
.rounded-base { border-radius: var(--radius-base-fluid) !important; }
.rounded-lg { border-radius: var(--radius-lg-fluid) !important; }
.rounded-xl { border-radius: var(--radius-xl-fluid) !important; }

/* 全局强制网格布局规则 - 覆盖所有媒体查询 */
.responsive-grid,
.info-cards-section,
.stats-cards,
.time-weather-section,
.data-grid,
.quick-overview,
.secondary-data-grid,
.bottom-data-grid,
.info-grid-redesigned,
.actions-grid {
  grid-template-columns: repeat(2, 1fr) !important;
  gap: var(--space-base-fluid) !important;
}

/* 特殊情况：某些网格可能需要不同的列数 */
.grid-1-col { grid-template-columns: 1fr !important; }
.grid-3-col { grid-template-columns: repeat(3, 1fr) !important; }
.grid-4-col { grid-template-columns: repeat(4, 1fr) !important; }

/* 确保所有文字和间距使用流体缩放 */
* {
  font-size: inherit;
}

/* 覆盖任何固定的rpx值 */
[class*="gap-"] {
  gap: var(--space-base-fluid) !important;
}

[class*="padding-"] {
  padding: var(--space-base-fluid) !important;
}

[class*="margin-"] {
  margin: var(--space-base-fluid) !important;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: linear-gradient(120deg, #1a1a2e 0%, #16213e 100%);
    --bg-secondary: #2a2a3e;
    --bg-card: #3a3a4e;
    --bg-overlay: rgba(58, 58, 78, 0.95);
    
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #999999;
    --text-light: #666666;
    
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.4);
    --shadow-xl: 0 20px 60px rgba(0, 0, 0, 0.5);
  }
}
