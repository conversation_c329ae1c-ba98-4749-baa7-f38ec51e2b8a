/**
 * 动态尺寸调整系统
 * 基于iPhone 14 Pro Max (430px) 为标准的动态缩放系统
 * 确保在不同屏幕宽度下保持相对比例不变
 */

// iPhone 14 Pro Max 基准尺寸
const REFERENCE_WIDTH = 430;
const REFERENCE_HEIGHT = 932;

class DynamicScaleManager {
  constructor() {
    this.currentScale = 1;
    this.deviceInfo = null;
    this.isInitialized = false;
    this.listeners = [];
  }

  /**
   * 初始化动态缩放管理器
   */
  async init() {
    if (this.isInitialized) return;

    try {
      this.deviceInfo = await this.getDeviceInfo();
      this.currentScale = this.calculateScale();
      this.isInitialized = true;
      
      // 应用全局缩放
      this.applyGlobalScale();
      
      // 监听屏幕方向变化
      this.setupOrientationListener();
      
      console.log('DynamicScaleManager initialized:', {
        deviceInfo: this.deviceInfo,
        scale: this.currentScale
      });
    } catch (error) {
      console.error('Failed to initialize DynamicScaleManager:', error);
    }
  }

  /**
   * 获取设备信息
   */
  getDeviceInfo() {
    return new Promise((resolve) => {
      uni.getSystemInfo({
        success: (res) => {
          resolve({
            screenWidth: res.screenWidth,
            screenHeight: res.screenHeight,
            windowWidth: res.windowWidth,
            windowHeight: res.windowHeight,
            pixelRatio: res.pixelRatio,
            platform: res.platform,
            model: res.model
          });
        },
        fail: () => {
          resolve({
            screenWidth: 375,
            screenHeight: 667,
            windowWidth: 375,
            windowHeight: 667,
            pixelRatio: 2,
            platform: 'unknown',
            model: 'unknown'
          });
        }
      });
    });
  }

  /**
   * 计算缩放比例
   */
  calculateScale() {
    if (!this.deviceInfo) return 1;

    const { windowWidth } = this.deviceInfo;
    
    // 基于宽度计算缩放比例
    let scale = windowWidth / REFERENCE_WIDTH;
    
    // 限制缩放范围，避免过度缩放
    scale = Math.max(0.7, Math.min(1.3, scale));
    
    return scale;
  }

  /**
   * 应用全局缩放
   */
  applyGlobalScale() {
    const scale = this.currentScale;
    
    // 设置CSS自定义属性
    const root = document.documentElement || document.querySelector('html');
    if (root) {
      root.style.setProperty('--dynamic-scale', scale);
      root.style.setProperty('--dynamic-scale-inverse', 1 / scale);
    }

    // 通知所有监听器
    this.notifyListeners(scale);
  }

  /**
   * 设置屏幕方向监听器
   */
  setupOrientationListener() {
    uni.onWindowResize(() => {
      setTimeout(async () => {
        this.deviceInfo = await this.getDeviceInfo();
        const newScale = this.calculateScale();
        
        if (Math.abs(newScale - this.currentScale) > 0.01) {
          this.currentScale = newScale;
          this.applyGlobalScale();
        }
      }, 100);
    });
  }

  /**
   * 添加缩放变化监听器
   */
  addListener(callback) {
    this.listeners.push(callback);
    
    if (this.isInitialized) {
      callback(this.currentScale);
    }
  }

  /**
   * 移除监听器
   */
  removeListener(callback) {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 通知所有监听器
   */
  notifyListeners(scale) {
    this.listeners.forEach(callback => {
      try {
        callback(scale);
      } catch (error) {
        console.error('Error in scale listener:', error);
      }
    });
  }

  /**
   * 获取当前缩放比例
   */
  getCurrentScale() {
    return this.currentScale;
  }

  /**
   * 根据缩放比例调整数值
   */
  scaleValue(value, type = 'size') {
    const scale = this.currentScale;
    
    if (typeof value === 'number') {
      return Math.round(value * scale);
    }
    
    if (typeof value === 'string') {
      // 处理rpx单位
      if (value.includes('rpx')) {
        const num = parseFloat(value);
        return `${Math.round(num * scale)}rpx`;
      }
      
      // 处理px单位
      if (value.includes('px')) {
        const num = parseFloat(value);
        return `${Math.round(num * scale)}px`;
      }
      
      // 处理复合值
      if (value.includes(' ')) {
        return value.split(' ').map(v => this.scaleValue(v, type)).join(' ');
      }
    }
    
    return value;
  }

  /**
   * 获取响应式样式对象
   */
  getScaledStyles(baseStyles) {
    const scaledStyles = { ...baseStyles };
    const scale = this.currentScale;
    
    // 需要缩放的属性
    const scalableProps = [
      'fontSize', 'padding', 'margin', 'borderRadius', 'width', 'height',
      'paddingTop', 'paddingRight', 'paddingBottom', 'paddingLeft',
      'marginTop', 'marginRight', 'marginBottom', 'marginLeft',
      'gap', 'rowGap', 'columnGap', 'lineHeight', 'letterSpacing',
      'borderWidth', 'outlineWidth'
    ];
    
    scalableProps.forEach(prop => {
      if (scaledStyles[prop] !== undefined) {
        scaledStyles[prop] = this.scaleValue(scaledStyles[prop]);
      }
    });
    
    return scaledStyles;
  }

  /**
   * 创建响应式CSS类
   */
  createScaledClass(baseClass) {
    const scale = this.currentScale;
    const scaleLevel = this.getScaleLevel(scale);
    return `${baseClass} ${baseClass}--scale-${scaleLevel}`;
  }

  /**
   * 获取缩放级别
   */
  getScaleLevel(scale) {
    if (scale <= 0.8) return 'xs';
    if (scale <= 0.9) return 'sm';
    if (scale <= 0.95) return 'md-';
    if (scale <= 1.05) return 'md';
    if (scale <= 1.1) return 'md+';
    if (scale <= 1.2) return 'lg';
    return 'xl';
  }
}

// 创建全局实例
const dynamicScaleManager = new DynamicScaleManager();

// 导出
export default dynamicScaleManager;
export { DynamicScaleManager };

// 自动初始化
export const initDynamicScaleManager = async () => {
  await dynamicScaleManager.init();
  return dynamicScaleManager;
};
