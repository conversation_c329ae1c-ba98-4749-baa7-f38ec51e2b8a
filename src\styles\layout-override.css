/**
 * 布局强制覆盖样式
 * 确保在所有屏幕尺寸下保持一致的布局结构
 * 优先级最高，覆盖所有其他样式
 */

/* 强制所有网格保持2列布局 - 最高优先级 */
.responsive-grid,
.info-cards-section,
.stats-cards,
.time-weather-section,
.data-grid,
.quick-overview,
.secondary-data-grid,
.bottom-data-grid,
.info-grid-redesigned,
.actions-grid,
.grid-2-cols,
.monitoring-grid,
.card-grid,
.feature-grid {
  display: grid !important;
  grid-template-columns: repeat(2, 1fr) !important;
  grid-template-rows: auto !important;
  gap: clamp(8rpx, 2vw, 24rpx) !important;
  width: 100% !important;
  box-sizing: border-box !important;
  /* 防止flex布局覆盖 */
  flex-direction: unset !important;
  flex-wrap: unset !important;
}

/* 覆盖所有可能的断点类 */
.breakpoint-xs .responsive-grid,
.breakpoint-xs .info-cards-section,
.breakpoint-xs .stats-cards,
.breakpoint-xs .time-weather-section,
.breakpoint-xs .data-grid,
.breakpoint-xs .quick-overview,
.breakpoint-xs .secondary-data-grid,
.breakpoint-xs .bottom-data-grid,
.breakpoint-xs .info-grid-redesigned,
.breakpoint-xs .actions-grid,
.breakpoint-sm .responsive-grid,
.breakpoint-sm .info-cards-section,
.breakpoint-sm .stats-cards,
.breakpoint-sm .time-weather-section,
.breakpoint-sm .data-grid,
.breakpoint-sm .quick-overview,
.breakpoint-sm .secondary-data-grid,
.breakpoint-sm .bottom-data-grid,
.breakpoint-sm .info-grid-redesigned,
.breakpoint-sm .actions-grid,
.breakpoint-md .responsive-grid,
.breakpoint-md .info-cards-section,
.breakpoint-md .stats-cards,
.breakpoint-md .time-weather-section,
.breakpoint-md .data-grid,
.breakpoint-md .quick-overview,
.breakpoint-md .secondary-data-grid,
.breakpoint-md .bottom-data-grid,
.breakpoint-md .info-grid-redesigned,
.breakpoint-md .actions-grid {
  grid-template-columns: repeat(2, 1fr) !important;
  grid-template-rows: auto !important;
  gap: clamp(8rpx, 2vw, 24rpx) !important;
}

/* 覆盖所有媒体查询 */
@media screen and (max-width: 320px) {
  .responsive-grid,
  .info-cards-section,
  .stats-cards,
  .time-weather-section,
  .data-grid,
  .quick-overview,
  .secondary-data-grid,
  .bottom-data-grid,
  .info-grid-redesigned,
  .actions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: clamp(6rpx, 1.5vw, 16rpx) !important;
    font-size: clamp(20rpx, 3vw, 28rpx) !important;
  }
}

@media screen and (min-width: 321px) and (max-width: 360px) {
  .responsive-grid,
  .info-cards-section,
  .stats-cards,
  .time-weather-section,
  .data-grid,
  .quick-overview,
  .secondary-data-grid,
  .bottom-data-grid,
  .info-grid-redesigned,
  .actions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: clamp(8rpx, 2vw, 18rpx) !important;
    font-size: clamp(22rpx, 3.2vw, 30rpx) !important;
  }
}

@media screen and (min-width: 361px) and (max-width: 400px) {
  .responsive-grid,
  .info-cards-section,
  .stats-cards,
  .time-weather-section,
  .data-grid,
  .quick-overview,
  .secondary-data-grid,
  .bottom-data-grid,
  .info-grid-redesigned,
  .actions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: clamp(10rpx, 2.2vw, 20rpx) !important;
    font-size: clamp(24rpx, 3.5vw, 32rpx) !important;
  }
}

@media screen and (min-width: 401px) and (max-width: 480px) {
  .responsive-grid,
  .info-cards-section,
  .stats-cards,
  .time-weather-section,
  .data-grid,
  .quick-overview,
  .secondary-data-grid,
  .bottom-data-grid,
  .info-grid-redesigned,
  .actions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: clamp(12rpx, 2.5vw, 22rpx) !important;
    font-size: clamp(26rpx, 3.8vw, 34rpx) !important;
  }
}

@media screen and (min-width: 481px) {
  .responsive-grid,
  .info-cards-section,
  .stats-cards,
  .time-weather-section,
  .data-grid,
  .quick-overview,
  .secondary-data-grid,
  .bottom-data-grid,
  .info-grid-redesigned,
  .actions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: clamp(14rpx, 3vw, 28rpx) !important;
    font-size: clamp(28rpx, 4vw, 36rpx) !important;
  }
}

/* 强制覆盖任何可能的单列布局 */
[style*="grid-template-columns: 1fr"],
[style*="grid-template-columns:1fr"] {
  grid-template-columns: repeat(2, 1fr) !important;
}

/* 确保卡片内容也使用响应式尺寸 */
.info-card,
.stat-card,
.data-item,
.card-item,
.grid-item {
  padding: clamp(12rpx, 2.5vw, 24rpx) !important;
  border-radius: clamp(8rpx, 2vw, 16rpx) !important;
  font-size: clamp(24rpx, 3.5vw, 32rpx) !important;
}

.info-card .info-label,
.stat-card .stat-label,
.data-item .data-label {
  font-size: clamp(20rpx, 3vw, 28rpx) !important;
}

.info-card .info-value,
.stat-card .stat-value,
.data-item .data-value {
  font-size: clamp(28rpx, 4vw, 40rpx) !important;
  font-weight: 600 !important;
}

/* 确保数字在一行显示 */
.dose-rate-number,
.stat-value,
.data-value,
.metric-value {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: block !important;
  width: 100% !important;
}

/* 确保单位文字也在一行 */
.dose-rate-unit,
.stat-unit,
.data-unit,
.metric-unit {
  white-space: nowrap !important;
  font-size: clamp(18rpx, 2.5vw, 24rpx) !important;
}

/* 容器响应式调整 */
.container,
.index-container,
.dashboard-container {
  padding: clamp(12rpx, 2.5vw, 24rpx) !important;
  width: 100% !important;
  max-width: 100vw !important;
  box-sizing: border-box !important;
}

/* 确保所有图标大小适配 */
.icon,
.info-icon,
.data-icon,
.stat-icon {
  width: clamp(32rpx, 5vw, 48rpx) !important;
  height: clamp(32rpx, 5vw, 48rpx) !important;
}

/* 确保按钮大小适配 */
.btn,
.button,
.action-btn {
  padding: clamp(8rpx, 2vw, 16rpx) clamp(16rpx, 3vw, 24rpx) !important;
  font-size: clamp(24rpx, 3.5vw, 32rpx) !important;
  border-radius: clamp(8rpx, 2vw, 16rpx) !important;
}

/* 防止任何transform缩放影响布局 */
.responsive-container,
.scale-container {
  transform: none !important;
  width: 100% !important;
}
